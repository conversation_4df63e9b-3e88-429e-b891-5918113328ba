import axios from 'axios'
import * as cheerio from 'cheerio'

interface GoogleProductResult {
  title: string
  price?: string
  source?: string
  link?: string
  thumbnail?: string
}

class GoogleLensTest {
  async getGoogleLensResults(url: string): Promise<any> {
    const headers = {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/91.0.4472.77 Mobile/15E148 Safari/604.1',
      'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8'
    }

    const endpoint = 'https://lens.google.com/uploadbyurl?url=' + encodeURIComponent(url)
    console.log('Endpoint:', endpoint)

    for (let i = 0; i < 10; i++) {
      try {
        console.log(`Attempt ${i + 1}/10...`)
        const response = await axios.get(endpoint, { headers, timeout: 60000 })
        console.log(`Google Lens response status: ${response.status}`)
        return response.data
      } catch (error) {
        console.error(`Attempt ${i + 1} failed:`, error.message)
        if (i === 9) {
          throw new Error('Failed to fetch Google Lens results after 10 attempts.')
        }
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }

  async extractGoogleLensProducts(url: string): Promise<GoogleProductResult[]> {
    try {
      console.log('Fetching Google Lens results...')
      const html = await this.getGoogleLensResults(url)

      // Save the HTML response for debugging
      const fs = require('fs')
      fs.writeFileSync('google-lens-response.html', html)
      console.log('HTML response saved to google-lens-response.html')

      const $ = cheerio.load(html)
      let raw_data = ''
      let scriptCount = 0

      $('script').each((_, element) => {
        const textContent = $(element).text()
        scriptCount++

        if (textContent.startsWith('AF_initDataCallback')) {
          console.log(`Found AF_initDataCallback script #${scriptCount}, length: ${textContent.length}`)
          if (!textContent.includes('Unknown Type')) {
            raw_data = textContent
            console.log('Selected this script for processing')
          } else {
            console.log('Skipped due to Unknown Type')
          }
        }
      })

      console.log(`Total scripts found: ${scriptCount}`)

      if (!raw_data) {
        console.log('No suitable AF_initDataCallback script found')

        // Let's also check for other potential data sources
        $('script').each((_, element) => {
          const textContent = $(element).text()
          if (textContent.includes('data:') && textContent.length > 1000) {
            console.log(`Found potential data script, length: ${textContent.length}`)
            console.log('First 200 chars:', textContent.substring(0, 200))
          }
        })

        return []
      }

      console.log('Raw data found, length:', raw_data.length)
      console.log('First 500 chars of raw data:', raw_data.substring(0, 500))

      // Try multiple patterns to extract JSON data
      let jsonData = null
      const patterns = [
        /data:(\[.*?\])/,
        /data:\s*(\[.*?\])/,
        /"data":\s*(\[.*?\])/,
        /AF_initDataCallback\({[^}]*data:\s*(\[.*?\])/
      ]

      for (const pattern of patterns) {
        const match = raw_data.match(pattern)
        if (match) {
          try {
            jsonData = JSON.parse(match[1])
            console.log('Successfully parsed JSON with pattern:', pattern.source)
            break
          } catch (e) {
            console.log('Failed to parse JSON with pattern:', pattern.source)
          }
        }
      }

      if (!jsonData) {
        console.log('No valid JSON data found with any pattern')
        return []
      }

      console.log('JSON data structure type:', typeof jsonData)
      console.log('JSON data is array:', Array.isArray(jsonData))

      // Save the parsed JSON for debugging
      fs.writeFileSync('google-lens-data.json', JSON.stringify(jsonData, null, 2))
      console.log('Parsed JSON data saved to google-lens-data.json')

      // Navigate through the nested structure to find products
      const products: GoogleProductResult[] = []

      function extractProducts(data: any, depth = 0, path = ''): void {
        if (depth > 15) return // Prevent infinite recursion

        if (Array.isArray(data)) {
          data.forEach((item, index) => extractProducts(item, depth + 1, `${path}[${index}]`))
        } else if (typeof data === 'object' && data !== null) {
          // Look for product-like structures
          if (data.title && typeof data.title === 'string') {
            console.log(`Found potential product at ${path}:`, data.title)
            products.push({
              title: data.title,
              price: data.price || undefined,
              source: data.source || undefined,
              link: data.link || undefined,
              thumbnail: data.thumbnail || undefined
            })
          }

          Object.entries(data).forEach(([key, value]) =>
            extractProducts(value, depth + 1, `${path}.${key}`)
          )
        }
      }

      extractProducts(jsonData)

      console.log(`Found ${products.length} products`)
      return products

    } catch (error) {
      console.error('Error extracting Google Lens products:', error)
      return []
    }
  }

  async testImageAnalysis(imageUrl: string) {
    console.log('='.repeat(50))
    console.log('Testing Google Lens with image:', imageUrl)
    console.log('='.repeat(50))

    try {
      // First, let's just try to get the raw response
      console.log('Making direct request to Google Lens...')
      const html = await this.getGoogleLensResults(imageUrl)

      console.log('\n' + '='.repeat(50))
      console.log('RAW HTML RESPONSE:')
      console.log('='.repeat(50))
      console.log('HTML length:', html.length)
      console.log('First 1000 characters:')
      console.log(html.substring(0, 1000))

      // Check if this looks like a redirect or error page
      if (html.includes('noscript') || html.includes('enablejs') || html.length < 1000) {
        console.log('\n⚠️  WARNING: This looks like a redirect or error page!')
        console.log('Google Lens may be blocking automated requests or requiring JavaScript.')

        // Try with different headers
        console.log('\nTrying with different headers...')
        const headers2 = {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        }

        const endpoint2 = 'https://lens.google.com/uploadbyurl?url=' + encodeURIComponent(imageUrl)
        try {
          const response2 = await axios.get(endpoint2, { headers: headers2, timeout: 30000 })
          console.log('Second attempt status:', response2.status)
          console.log('Second attempt HTML length:', response2.data.length)
          console.log('First 500 chars:', response2.data.substring(0, 500))
        } catch (e) {
          console.log('Second attempt failed:', e.message)
        }
      }

      const products = await this.extractGoogleLensProducts(imageUrl)

      console.log('\n' + '='.repeat(50))
      console.log('EXTRACTION RESULTS:')
      console.log('='.repeat(50))

      if (products.length === 0) {
        console.log('No products found')
      } else {
        products.forEach((product, index) => {
          console.log(`\nProduct ${index + 1}:`)
          console.log('Title:', product.title)
          if (product.price) console.log('Price:', product.price)
          if (product.source) console.log('Source:', product.source)
          if (product.link) console.log('Link:', product.link)
          if (product.thumbnail) console.log('Thumbnail:', product.thumbnail)
        })
      }

      console.log('\n' + '='.repeat(50))
      console.log(`Total products found: ${products.length}`)
      console.log('='.repeat(50))

    } catch (error) {
      console.error('Test failed:', error)
    }
  }
}

// Run the test
async function main() {
  const tester = new GoogleLensTest()
  const imageUrl = 'https://shopmyfeedprod.s3.amazonaws.com/instagram/627c3fd5-ac85-4db6-8f62-71b9602f3842/3491244273368522944_1658786272/3491244253453816921_1658786272.webp'

  await tester.testImageAnalysis(imageUrl)
}

main().catch(console.error)
